# Compiler
CC = gcc

# Compiler flags
CFLAGS = -Wall -Wextra -std=c99

# Source file
SRCS = exercise.c

# Object files
OBJS = $(SRCS:.c=.o)

# Executable name (add .exe on Windows automatically)
EXEC = exercise
ifeq ($(OS),Windows_NT)
    EXEC := $(EXEC).exe
    RM := del /Q
else
    RM := rm -f
endif

# Default target
all: $(EXEC)

# Rule to compile source files into object files
%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

# Rule to link object files into the executable
$(EXEC): $(OBJS)
	$(CC) $(OBJS) -o $(EXEC) -mconsole

# Clean up object files and the executable
clean:
	$(RM) $(OBJS) $(EXEC)

# Phony targets
.PHONY: all clean