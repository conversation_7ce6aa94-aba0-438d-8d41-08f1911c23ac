#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>

// Case-insensitive string comparison function
int stricmp(const char *str1, const char *str2) {
    while (*str1 && *str2) {
        int c1 = tolower((unsigned char)*str1);
        int c2 = tolower((unsigned char)*str2);
        if (c1 != c2) return c1 - c2;
        str1++;
        str2++;
    }
    return tolower((unsigned char)*str1) - tolower((unsigned char)*str2);
}

typedef struct {
    char username[50];
    char password[50];
    int status;   // 1 = active, 0 = blocked
    int score;
} User;

typedef struct Node {
    User data;
    struct Node *next;
} Node;

Node *head = NULL;

// Load users from file
void loadFromFile() {
    FILE *f = fopen("user.txt", "r");
    if (!f) return;
    head = NULL;
    User u;
    while (fscanf(f, "%[^:]:%[^:]:%d:%d\n", u.username, u.password, &u.status, &u.score) == 4) {
        Node *newNode = (Node*)malloc(sizeof(Node));
        newNode->data = u;
        newNode->next = head;
        head = newNode;
    }
    fclose(f);
}

// Save users back to file
void saveToFile() {
    FILE *f = fopen("user.txt", "w");
    if (!f) return;
    Node *cur = head;
    while (cur) {
        fprintf(f, "%s:%s:%d:%d\n", cur->data.username, cur->data.password, cur->data.status, cur->data.score);
        cur = cur->next;
    }
    fclose(f);
}

// Find user by username (case-insensitive)
Node* findUser(const char *username) {
    Node *cur = head;
    while (cur) {
        if (stricmp(cur->data.username, username) == 0) return cur;
        cur = cur->next;
    }
    return NULL;
}

// Register function
void registerUser() {
    char username[50], password[50];
    int score;
    printf("Username: ");
    scanf("%s", username);

    if (findUser(username)) {
        printf("Account existed\n");
        return;
    }

    printf("Password: ");
    scanf("%s", password);
    printf("Score: ");
    scanf("%d", &score);

    User u;
    strcpy(u.username, username);
    strcpy(u.password, password);
    u.status = 1; // active
    u.score = score;

    Node *newNode = (Node*)malloc(sizeof(Node));
    newNode->data = u;
    newNode->next = head;
    head = newNode;

    saveToFile();
    printf("Successful registration\n");
}

// Sign in function
void signIn() {
    char username[50], password[50];
    printf("Username: ");
    scanf("%s", username);
    Node *u = findUser(username);

    if (!u) {
        printf("Cannot find account\n");
        return;
    }
    if (u->data.status == 0) {
        printf("Account is blocked\n");
        return;
    }

    int tries = 0;
    while (tries < 3) {
        printf("Password: ");
        scanf("%s", password);
        if (strcmp(password, u->data.password) == 0) {
            printf("Hello %s\n", u->data.username);
            return;
        } else {
            tries++;
            if (tries >= 3) {
                u->data.status = 0;
                saveToFile();
                printf("Password is incorrect. Account is blocked\n");
                return;
            } else {
                printf("Password is incorrect\n");
            }
        }
    }
}

// Search function
void searchUser() {
    char username[50];
    printf("Username: ");
    scanf("%s", username);
    Node *u = findUser(username);

    if (!u) {
        printf("Cannot find account\n");
        return;
    }
    if (u->data.status == 1)
        printf("Account is active\n");
    else
        printf("Account is blocked\n");
}

// Bubble sort
void bubbleSort() {
    if (!head) return;
    int swapped;
    Node *ptr1;
    Node *lptr = NULL;
    do {
        swapped = 0;
        ptr1 = head;
        while (ptr1->next != lptr) {
            if (ptr1->data.score < ptr1->next->data.score) {
                User temp = ptr1->data;
                ptr1->data = ptr1->next->data;
                ptr1->next->data = temp;
                swapped = 1;
            }
            ptr1 = ptr1->next;
        }
        lptr = ptr1;
    } while (swapped);
}

// Convert linked list to array
User* listToArray(int *size) {
    *size = 0;
    Node *cur = head;
    while (cur) {
        (*size)++;
        cur = cur->next;
    }
    if (*size == 0) return NULL;
    User *arr = (User*)malloc(*size * sizeof(User));
    cur = head;
    for (int i = 0; i < *size; i++) {
        arr[i] = cur->data;
        cur = cur->next;
    }
    return arr;
}

// Binary search for score > 5
void binarySearchScore(User *arr, int size) {
    int left = 0, right = size - 1;
    int firstIndex = -1;
    while (left <= right) {
        int mid = (left + right) / 2;
        if (arr[mid].score > 5) {
            firstIndex = mid;
            right = mid - 1;
        } else {
            left = mid + 1;
        }
    }
    if (firstIndex != -1) {
        for (int i = firstIndex; i < size && arr[i].score > 5; i++) {
            printf("%s:%d\n", arr[i].username, arr[i].score);
        }
    }
}

// Sort and Search function
void sortAndSearch() {
    printf("Sort:\n");
    bubbleSort();
    Node *cur = head;
    while (cur) {
        printf("%s:%d\n", cur->data.username, cur->data.score);
        cur = cur->next;
    }
    printf("\nSearch:\n");
    int size;
    User *arr = listToArray(&size);
    if (arr) {
        binarySearchScore(arr, size);
        free(arr);
    }
}

int main() {
    int choice;
    loadFromFile();
    printf("USER MANAGEMENT PROGRAM\n");
    printf("-----------------------------------\n");
    do {
        printf("1. Register\n");
        printf("2. Sign in\n");
        printf("3. Search\n");
        printf("4. Sort\n");
        printf("Your choice (1-4, other to quit): ");
        scanf("%d", &choice);
        switch(choice) {
            case 1: registerUser(); break;
            case 2: signIn(); break;
            case 3: searchUser(); break;
            case 4: sortAndSearch(); break;
            default: return 0;
        }
    } while (1);
    return 0;
}
