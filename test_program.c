#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifdef _WIN32
    #define stricmp _stricmp
#else
    #define stricmp strcasecmp
#endif

typedef struct {
    char username[50];
    char password[50];
    int status;   // 1 = active, 0 = blocked
    int score;
} User;

typedef struct Node {
    User data;
    struct Node *next;
} Node;

Node *head = NULL;

// Load users from file
void loadFromFile() {
    FILE *f = fopen("user.txt", "r");
    if (!f) return;
    head = NULL;
    User u;
    while (fscanf(f, "%[^:]:%[^:]:%d:%d\n", u.username, u.password, &u.status, &u.score) == 4) {
        Node *newNode = (Node*)malloc(sizeof(Node));
        newNode->data = u;
        newNode->next = head;
        head = newNode;
    }
    fclose(f);
}

// Bubble sort
void bubbleSort() {
    if (!head) return;
    int swapped;
    Node *ptr1;
    Node *lptr = NULL;
    do {
        swapped = 0;
        ptr1 = head;
        while (ptr1->next != lptr) {
            if (ptr1->data.score < ptr1->next->data.score) {
                User temp = ptr1->data;
                ptr1->data = ptr1->next->data;
                ptr1->next->data = temp;
                swapped = 1;
            }
            ptr1 = ptr1->next;
        }
        lptr = ptr1;
    } while (swapped);
}

// Convert linked list to array
User* listToArray(int *size) {
    *size = 0;
    Node *cur = head;
    while (cur) {
        (*size)++;
        cur = cur->next;
    }
    if (*size == 0) return NULL;
    User *arr = (User*)malloc(*size * sizeof(User));
    cur = head;
    for (int i = 0; i < *size; i++) {
        arr[i] = cur->data;
        cur = cur->next;
    }
    return arr;
}

// Binary search for score > 5
void binarySearchScore(User *arr, int size) {
    int left = 0, right = size - 1;
    int firstIndex = -1;
    while (left <= right) {
        int mid = (left + right) / 2;
        if (arr[mid].score > 5) {
            firstIndex = mid;
            right = mid - 1;
        } else {
            left = mid + 1;
        }
    }
    if (firstIndex != -1) {
        for (int i = firstIndex; i < size; i++) {
            printf("%s:%d\n", arr[i].username, arr[i].score);
        }
    }
}

// Sort and Search function
void sortAndSearch() {
    printf("Sort:\n");
    bubbleSort();
    Node *cur = head;
    while (cur) {
        printf("%s:%d\n", cur->data.username, cur->data.score);
        cur = cur->next;
    }
    printf("\nSearch:\n");
    int size;
    User *arr = listToArray(&size);
    if (arr) {
        binarySearchScore(arr, size);
        free(arr);
    }
}

int main() {
    printf("Starting test program...\n");
    loadFromFile();
    printf("Data loaded from file.\n");
    sortAndSearch();
    printf("Test completed.\n");
    return 0;
}
