#include <stdio.h>
#include <stdlib.h>
#include <string.h>

typedef struct {
    char username[50];
    char password[50];
    int status;
    int score;
} User;

typedef struct Node {
    User data;
    struct Node *next;
} Node;

Node *head = NULL;

void addUser(const char* username, const char* password, int status, int score) {
    Node *newNode = (Node*)malloc(sizeof(Node));
    strcpy(newNode->data.username, username);
    strcpy(newNode->data.password, password);
    newNode->data.status = status;
    newNode->data.score = score;
    newNode->next = head;
    head = newNode;
}

void bubbleSort() {
    if (!head) return;
    int swapped;
    Node *ptr1;
    Node *lptr = NULL;
    do {
        swapped = 0;
        ptr1 = head;
        while (ptr1->next != lptr) {
            if (ptr1->data.score < ptr1->next->data.score) {
                User temp = ptr1->data;
                ptr1->data = ptr1->next->data;
                ptr1->next->data = temp;
                swapped = 1;
            }
            ptr1 = ptr1->next;
        }
        lptr = ptr1;
    } while (swapped);
}

User* listToArray(int *size) {
    *size = 0;
    Node *cur = head;
    while (cur) {
        (*size)++;
        cur = cur->next;
    }
    if (*size == 0) return NULL;
    User *arr = (User*)malloc(*size * sizeof(User));
    cur = head;
    for (int i = 0; i < *size; i++) {
        arr[i] = cur->data;
        cur = cur->next;
    }
    return arr;
}

void binarySearchScore(User *arr, int size) {
    int left = 0, right = size - 1;
    int firstIndex = -1;
    while (left <= right) {
        int mid = (left + right) / 2;
        if (arr[mid].score > 5) {
            firstIndex = mid;
            right = mid - 1;
        } else {
            left = mid + 1;
        }
    }
    if (firstIndex != -1) {
        for (int i = firstIndex; i < size; i++) {
            printf("%s:%d\n", arr[i].username, arr[i].score);
        }
    }
}

int main() {
    // Add test data
    addUser("hust", "h", 1, 2);
    addUser("soict", "soictfit", 1, 8);
    addUser("Hust", "hust123", 1, 4);
    addUser("Hedspi", "hedpsi2016", 0, 10);
    
    printf("Sort:\n");
    bubbleSort();
    Node *cur = head;
    while (cur) {
        printf("%s:%d\n", cur->data.username, cur->data.score);
        cur = cur->next;
    }
    
    printf("\nSearch:\n");
    int size;
    User *arr = listToArray(&size);
    if (arr) {
        binarySearchScore(arr, size);
        free(arr);
    }
    
    return 0;
}
